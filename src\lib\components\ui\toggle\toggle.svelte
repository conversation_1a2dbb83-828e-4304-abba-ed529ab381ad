<script lang="ts" module>
  import { type VariantProps, tv } from 'tailwind-variants';

  export const toggleVariants = tv({
    base: 'hover:bg-muted hover:text-muted-foreground focus-visible:ring-ring data-[state=on]:bg-primary data-[state=on]:text-primary-foreground inline-flex items-center justify-center gap-1 rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-6 [&_svg]:shrink-0',
    variants: {
      variant: {
        default: 'bg-transparent',
        outline:
          'border-input hover:bg-primary/80 hover:text-primary-foreground border bg-transparent'
      },
      size: {
        default: 'h-9 min-w-9 px-3',
        sm: 'h-8 min-w-8 px-2',
        lg: 'h-10 min-w-10 px-3'
      }
    },
    defaultVariants: {
      variant: 'default',
      size: 'default'
    }
  });

  export type ToggleVariant = VariantProps<typeof toggleVariants>['variant'];
  export type ToggleSize = VariantProps<typeof toggleVariants>['size'];
  export type ToggleVariants = VariantProps<typeof toggleVariants>;
</script>

<script lang="ts">
  import { cn } from '$lib/utils.js';
  import { Toggle as TogglePrimitive } from 'bits-ui';

  let {
    ref = $bindable(null),
    pressed = $bindable(false),
    class: className,
    size = 'default',
    variant = 'default',
    ...restProps
  }: TogglePrimitive.RootProps & {
    variant?: ToggleVariant;
    size?: ToggleSize;
  } = $props();
</script>

<TogglePrimitive.Root
  bind:ref
  bind:pressed
  class={cn(toggleVariants({ variant, size }), className)}
  {...restProps} />
