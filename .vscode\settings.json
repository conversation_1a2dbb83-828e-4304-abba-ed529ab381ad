{"editor.formatOnSave": true, "cSpell.blockCheckingWhenLineLengthGreaterThan": 150, "cSpell.words": ["appinstalled", "asyncable", "Browserslist", "ckppp", "corg", "cssnano", "esserializer", "fsegurai", "gantt", "gitgraph", "KROKI", "localstorage", "mermaidchart", "mindmap", "Pageview", "pako", "panmove", "panstart", "panzoom", "pinchmove", "pinchstart", "pzoom", "roughjs", "sankey", "Ser<PERSON>", "serdes", "Stackable", "tailwindcss", "treemap", "uparrow", "<PERSON><PERSON><PERSON>", "zenuml"], "vitest.commandLine": "pnpm test:unit", "vitest.enable": true, "testing.autoRun.mode": "rerun", "svelte.enable-ts-plugin": true, "githubPullRequests.ignoredPullRequestBranches": ["develop"], "[svelte]": {"editor.defaultFormatter": "svelte.svelte-vscode"}, "tailwindCSS.classAttributes": ["class", "className", ".*Classes"], "tailwindCSS.experimental.classRegex": [["([\"'`][^\"'`]*.*?[\"'`])", "[\"'`]([^\"'`]*).*?[\"'`]"]], "tailwindCSS.emmetCompletions": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}}