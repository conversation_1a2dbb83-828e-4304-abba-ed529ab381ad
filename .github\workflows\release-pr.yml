name: Create release pull request
on:
  push:
    branches:
      - develop
jobs:
  productionPromotion:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          ref: master
      - name: Reset promotion branch
        run: |
          git fetch origin develop:develop
          git reset --hard develop
      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          branch: release-promotion
          title: Release live editor
